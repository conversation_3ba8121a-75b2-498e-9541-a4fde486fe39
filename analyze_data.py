#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Phân tích dữ liệu từ cổng dữ liệu mở Hải <PERSON>ng
Tác giả: AI Assistant
Ngày: 2024-10-29
"""

import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from collections import Counter, defaultdict
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Thiết lập font cho tiếng Việt
plt.rcParams['font.family'] = ['DejaVu Sans', 'Arial Unicode MS', 'Tahoma']
plt.rcParams['axes.unicode_minus'] = False

def load_data():
    """Tải dữ liệu từ 2 file JSON"""
    print("Đang tải dữ liệu...")
    
    # Đọc file 1
    with open('response_1_1000.json', 'r', encoding='utf-8') as f:
        data1 = json.load(f)
    
    # Đọc file 2  
    with open('response_1000_2000.json', 'r', encoding='utf-8') as f:
        data2 = json.load(f)
    
    # Kết hợp dữ liệu
    all_data = data1['_embedded'] + data2['_embedded']
    print(f"Tổng số dataset: {len(all_data)}")
    
    return all_data

def analyze_basic_stats(data):
    """Phân tích thống kê cơ bản"""
    print("\n" + "="*60)
    print("1. PHÂN TÍCH THỐNG KÊ CƠ BẢN")
    print("="*60)
    
    # Tổng số dataset
    total_datasets = len(data)
    print(f"Tổng số dataset: {total_datasets}")
    
    # Số dataset đang hoạt động
    active_datasets = sum(1 for item in data if item.get('KichHoat', False))
    print(f"Số dataset đang hoạt động: {active_datasets}")
    print(f"Tỷ lệ dataset hoạt động: {active_datasets/total_datasets*100:.1f}%")
    
    # Số dataset mở
    open_datasets = sum(1 for item in data if item.get('isopen', False))
    print(f"Số dataset mở: {open_datasets}")
    print(f"Tỷ lệ dataset mở: {open_datasets/total_datasets*100:.1f}%")
    
    return {
        'total': total_datasets,
        'active': active_datasets,
        'open': open_datasets
    }

def analyze_by_organization(data):
    """Phân tích theo tổ chức"""
    print("\n" + "="*60)
    print("2. PHÂN TÍCH THEO TỔ CHỨC")
    print("="*60)
    
    org_counter = Counter()
    org_details = defaultdict(list)
    
    for item in data:
        org_name = item.get('author', 'Không xác định')
        org_counter[org_name] += 1
        org_details[org_name].append(item.get('title', ''))
    
    print(f"Số tổ chức tham gia: {len(org_counter)}")
    print("\nTop 10 tổ chức có nhiều dataset nhất:")
    
    for i, (org, count) in enumerate(org_counter.most_common(10), 1):
        print(f"{i:2d}. {org}: {count} dataset")
    
    return org_counter, org_details

def analyze_by_fields(data):
    """Phân tích theo số lượng trường dữ liệu"""
    print("\n" + "="*60)
    print("3. PHÂN TÍCH THEO SỐ TRƯỜNG DỮ LIỆU")
    print("="*60)
    
    field_counts = []
    for item in data:
        fields = item.get('fields', [])
        if isinstance(fields, list):
            field_counts.append(len(fields))
        else:
            field_counts.append(0)
    
    if field_counts:
        print(f"Trung bình số trường/dataset: {np.mean(field_counts):.1f}")
        print(f"Số trường tối thiểu: {min(field_counts)}")
        print(f"Số trường tối đa: {max(field_counts)}")
        print(f"Trung vị: {np.median(field_counts):.1f}")
    
    return field_counts

def analyze_resources(data):
    """Phân tích tài nguyên (resources)"""
    print("\n" + "="*60)
    print("4. PHÂN TÍCH TÀI NGUYÊN")
    print("="*60)
    
    total_resources = 0
    datasets_with_resources = 0
    resource_formats = Counter()
    
    for item in data:
        package = item.get('package', {})
        resources = package.get('resources', [])
        num_resources = len(resources)
        
        total_resources += num_resources
        if num_resources > 0:
            datasets_with_resources += 1
            
        # Đếm định dạng file
        for resource in resources:
            format_type = resource.get('format', 'Không xác định')
            resource_formats[format_type] += 1
    
    print(f"Tổng số tài nguyên: {total_resources}")
    print(f"Dataset có tài nguyên: {datasets_with_resources}")
    print(f"Tỷ lệ dataset có tài nguyên: {datasets_with_resources/len(data)*100:.1f}%")
    
    print("\nĐịnh dạng file phổ biến:")
    for format_type, count in resource_formats.most_common():
        print(f"  {format_type}: {count} file")
    
    return resource_formats, datasets_with_resources

def create_visualizations(basic_stats, org_counter, field_counts, resource_formats):
    """Tạo các biểu đồ trực quan"""
    print("\n" + "="*60)
    print("5. TẠO BIỂU ĐỒ TRỰC QUAN")
    print("="*60)
    
    # Thiết lập style
    plt.style.use('default')
    sns.set_palette("husl")
    
    # Tạo figure với nhiều subplot
    fig = plt.figure(figsize=(20, 15))
    
    # 1. Biểu đồ tổng quan dataset
    ax1 = plt.subplot(2, 3, 1)
    categories = ['Tổng số', 'Đang hoạt động', 'Mở công khai']
    values = [basic_stats['total'], basic_stats['active'], basic_stats['open']]
    colors = ['#3498db', '#2ecc71', '#e74c3c']
    
    bars = plt.bar(categories, values, color=colors, alpha=0.8)
    plt.title('Tổng quan Dataset', fontsize=14, fontweight='bold')
    plt.ylabel('Số lượng')
    
    # Thêm giá trị lên đầu cột
    for bar, value in zip(bars, values):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                str(value), ha='center', va='bottom', fontweight='bold')
    
    # 2. Top 10 tổ chức
    ax2 = plt.subplot(2, 3, 2)
    top_orgs = dict(org_counter.most_common(10))
    org_names = [name[:30] + '...' if len(name) > 30 else name for name in top_orgs.keys()]
    
    plt.barh(range(len(top_orgs)), list(top_orgs.values()), color='skyblue')
    plt.yticks(range(len(top_orgs)), org_names)
    plt.xlabel('Số dataset')
    plt.title('Top 10 Tổ chức có nhiều Dataset nhất', fontsize=14, fontweight='bold')
    plt.gca().invert_yaxis()
    
    # 3. Phân bố số trường dữ liệu
    ax3 = plt.subplot(2, 3, 3)
    if field_counts:
        plt.hist(field_counts, bins=20, color='lightgreen', alpha=0.7, edgecolor='black')
        plt.axvline(np.mean(field_counts), color='red', linestyle='--', 
                   label=f'Trung bình: {np.mean(field_counts):.1f}')
        plt.xlabel('Số trường dữ liệu')
        plt.ylabel('Số dataset')
        plt.title('Phân bố Số trường Dữ liệu', fontsize=14, fontweight='bold')
        plt.legend()
    
    # 4. Định dạng file
    ax4 = plt.subplot(2, 3, 4)
    if resource_formats:
        formats = list(resource_formats.keys())
        counts = list(resource_formats.values())
        
        plt.pie(counts, labels=formats, autopct='%1.1f%%', startangle=90)
        plt.title('Phân bố Định dạng File', fontsize=14, fontweight='bold')
    
    # 5. Tỷ lệ dataset hoạt động vs không hoạt động
    ax5 = plt.subplot(2, 3, 5)
    active_ratio = [basic_stats['active'], basic_stats['total'] - basic_stats['active']]
    labels = ['Hoạt động', 'Không hoạt động']
    colors = ['#2ecc71', '#e74c3c']
    
    plt.pie(active_ratio, labels=labels, autopct='%1.1f%%', colors=colors, startangle=90)
    plt.title('Tỷ lệ Dataset Hoạt động', fontsize=14, fontweight='bold')
    
    # 6. Xu hướng theo thời gian (nếu có dữ liệu thời gian)
    ax6 = plt.subplot(2, 3, 6)
    # Placeholder cho biểu đồ xu hướng
    months = ['T1', 'T2', 'T3', 'T4', 'T5', 'T6', 'T7', 'T8', 'T9', 'T10']
    sample_data = np.random.randint(5, 25, 10)
    
    plt.plot(months, sample_data, marker='o', linewidth=2, markersize=6)
    plt.title('Xu hướng Tạo Dataset (Mẫu)', fontsize=14, fontweight='bold')
    plt.xlabel('Tháng')
    plt.ylabel('Số dataset mới')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('haiphong_data_analysis.png', dpi=300, bbox_inches='tight')
    print("Đã lưu biểu đồ: haiphong_data_analysis.png")
    
    return fig

def main():
    """Hàm chính"""
    print("PHÂN TÍCH DỮ LIỆU CỔNG DỮ LIỆU MỞ HẢI PHÒNG")
    print("=" * 60)
    
    # Tải dữ liệu
    data = load_data()
    
    # Phân tích
    basic_stats = analyze_basic_stats(data)
    org_counter, org_details = analyze_by_organization(data)
    field_counts = analyze_by_fields(data)
    resource_formats, datasets_with_resources = analyze_resources(data)
    
    # Tạo biểu đồ
    fig = create_visualizations(basic_stats, org_counter, field_counts, resource_formats)
    
    # Hiển thị biểu đồ
    plt.show()
    
    print("\n" + "="*60)
    print("HOÀN THÀNH PHÂN TÍCH!")
    print("="*60)

if __name__ == "__main__":
    main()

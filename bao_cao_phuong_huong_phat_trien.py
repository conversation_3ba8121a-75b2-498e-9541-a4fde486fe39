#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
T<PERSON><PERSON> báo cáo phương hướng phát triển và biểu đồ luồng
cho kho dữ liệu mở Hải Phòng
"""

import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from matplotlib.patches import FancyBboxPatch, ConnectionPatch
import numpy as np
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# Thiết lập font
plt.rcParams['font.family'] = ['DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def create_data_flow_diagram():
    """Tạo biểu đồ luồng dữ liệu tích hợp"""
    fig, ax = plt.subplots(1, 1, figsize=(16, 12))
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 10)
    ax.axis('off')
    
    # <PERSON><PERSON>u sắc
    colors = {
        'current': '#3498db',
        'new': '#2ecc71', 
        'api': '#e74c3c',
        'storage': '#f39c12',
        'user': '#9b59b6'
    }
    
    # Tiêu đề
    ax.text(5, 9.5, 'KIẾN TRÚC TÍCH HỢP KHO DỮ LIỆU MỞ HẢI PHÒNG', 
            ha='center', va='center', fontsize=16, fontweight='bold')
    
    # Lớp 1: Nguồn dữ liệu hiện tại
    current_sources = [
        ('Sở NN&PTNT\n(366 datasets)', 0.5, 8),
        ('Sở TT&TT\n(129 datasets)', 2.5, 8),
        ('Sở Xây dựng\n(102 datasets)', 4.5, 8),
        ('Sở GTVT\n(98 datasets)', 6.5, 8),
        ('Sở Y tế\n(92 datasets)', 8.5, 8)
    ]
    
    for text, x, y in current_sources:
        box = FancyBboxPatch((x-0.4, y-0.3), 0.8, 0.6, 
                            boxstyle="round,pad=0.1", 
                            facecolor=colors['current'], alpha=0.7)
        ax.add_patch(box)
        ax.text(x, y, text, ha='center', va='center', fontsize=9, fontweight='bold')
    
    # Lớp 2: API Gateway mới
    api_box = FancyBboxPatch((3.5, 6.2), 3, 0.8, 
                            boxstyle="round,pad=0.1", 
                            facecolor=colors['api'], alpha=0.8)
    ax.add_patch(api_box)
    ax.text(5, 6.6, 'API GATEWAY TÍCH HỢP', ha='center', va='center', 
            fontsize=12, fontweight='bold', color='white')
    ax.text(5, 6.3, 'Chuẩn hóa • Xác thực • Giám sát', ha='center', va='center', 
            fontsize=10, color='white')
    
    # Lớp 3: Nguồn dữ liệu mới
    new_sources = [
        ('API Giáo dục\n(MOET)', 1, 5),
        ('API Khu CN\n(Quản lý KCN)', 3, 5),
        ('API Cán bộ\n(Nội vụ)', 5, 5),
        ('API Công việc\n(Workflow)', 7, 5),
        ('API Khác\n(Mở rộng)', 9, 5)
    ]
    
    for text, x, y in new_sources:
        box = FancyBboxPatch((x-0.4, y-0.3), 0.8, 0.6, 
                            boxstyle="round,pad=0.1", 
                            facecolor=colors['new'], alpha=0.7)
        ax.add_patch(box)
        ax.text(x, y, text, ha='center', va='center', fontsize=9, fontweight='bold')
    
    # Lớp 4: Kho dữ liệu trung tâm
    storage_box = FancyBboxPatch((2, 3.2), 6, 1, 
                                boxstyle="round,pad=0.1", 
                                facecolor=colors['storage'], alpha=0.8)
    ax.add_patch(storage_box)
    ax.text(5, 3.9, 'KHO DỮ LIỆU TRUNG TÂM', ha='center', va='center', 
            fontsize=14, fontweight='bold', color='white')
    ax.text(5, 3.5, 'Data Lake • Data Warehouse • Metadata Management', 
            ha='center', va='center', fontsize=10, color='white')
    
    # Lớp 5: Dịch vụ đầu ra
    output_services = [
        ('Portal\nDữ liệu mở', 1.5, 2),
        ('API Public\nRESTful', 3.5, 2),
        ('Dashboard\nBI Analytics', 5.5, 2),
        ('Mobile App\nCitizen', 7.5, 2)
    ]
    
    for text, x, y in output_services:
        box = FancyBboxPatch((x-0.4, y-0.3), 0.8, 0.6, 
                            boxstyle="round,pad=0.1", 
                            facecolor=colors['user'], alpha=0.7)
        ax.add_patch(box)
        ax.text(x, y, text, ha='center', va='center', fontsize=9, 
                fontweight='bold', color='white')
    
    # Vẽ các mũi tên kết nối
    # Từ nguồn hiện tại đến API Gateway
    for _, x, y in current_sources:
        arrow = ConnectionPatch((x, y-0.3), (5, 7), "data", "data",
                              arrowstyle="->", shrinkA=5, shrinkB=5, 
                              mutation_scale=20, fc=colors['current'])
        ax.add_patch(arrow)
    
    # Từ nguồn mới đến API Gateway
    for _, x, y in new_sources:
        arrow = ConnectionPatch((x, y+0.3), (5, 6.2), "data", "data",
                              arrowstyle="->", shrinkA=5, shrinkB=5, 
                              mutation_scale=20, fc=colors['new'])
        ax.add_patch(arrow)
    
    # Từ API Gateway đến kho dữ liệu
    arrow = ConnectionPatch((5, 6.2), (5, 4.2), "data", "data",
                          arrowstyle="->", shrinkA=5, shrinkB=5, 
                          mutation_scale=25, fc=colors['api'], linewidth=3)
    ax.add_patch(arrow)
    
    # Từ kho dữ liệu đến dịch vụ đầu ra
    for _, x, y in output_services:
        arrow = ConnectionPatch((5, 3.2), (x, y+0.3), "data", "data",
                              arrowstyle="->", shrinkA=5, shrinkB=5, 
                              mutation_scale=20, fc=colors['storage'])
        ax.add_patch(arrow)
    
    # Chú thích
    legend_elements = [
        mpatches.Patch(color=colors['current'], label='Nguồn dữ liệu hiện tại'),
        mpatches.Patch(color=colors['new'], label='Nguồn dữ liệu mới'),
        mpatches.Patch(color=colors['api'], label='API Gateway'),
        mpatches.Patch(color=colors['storage'], label='Kho dữ liệu trung tâm'),
        mpatches.Patch(color=colors['user'], label='Dịch vụ người dùng')
    ]
    ax.legend(handles=legend_elements, loc='lower center', ncol=3, 
              bbox_to_anchor=(0.5, -0.05))
    
    plt.tight_layout()
    plt.savefig('luong_du_lieu_tich_hop.png', dpi=300, bbox_inches='tight')
    print("Đã tạo: luong_du_lieu_tich_hop.png")
    
    return fig

def create_implementation_roadmap():
    """Tạo biểu đồ lộ trình triển khai"""
    fig, ax = plt.subplots(1, 1, figsize=(16, 10))
    ax.set_xlim(0, 12)
    ax.set_ylim(0, 8)
    ax.axis('off')
    
    # Tiêu đề
    ax.text(6, 7.5, 'LỘ TRÌNH TRIỂN KHAI NÂNG CẤP KHO DỮ LIỆU', 
            ha='center', va='center', fontsize=16, fontweight='bold')
    
    # Các giai đoạn
    phases = [
        {
            'title': 'GIAI ĐOẠN 1\n(3 tháng)',
            'subtitle': 'Chuẩn hóa & Tối ưu',
            'tasks': [
                '• Chuẩn hóa tên trường',
                '• Tăng tỷ lệ dataset mở',
                '• Chuyển đổi sang CSV/JSON',
                '• Xây dựng API Gateway'
            ],
            'x': 2, 'y': 5.5, 'color': '#e74c3c'
        },
        {
            'title': 'GIAI ĐOẠN 2\n(6 tháng)',
            'subtitle': 'Tích hợp API',
            'tasks': [
                '• Kết nối API Giáo dục',
                '• Tích hợp API Khu CN',
                '• Xây dựng Data Lake',
                '• Phát triển Dashboard'
            ],
            'x': 6, 'y': 5.5, 'color': '#f39c12'
        },
        {
            'title': 'GIAI ĐOẠN 3\n(9 tháng)',
            'subtitle': 'Mở rộng & Tối ưu',
            'tasks': [
                '• API Quản lý cán bộ',
                '• API Workflow công việc',
                '• Mobile App Citizens',
                '• AI/ML Analytics'
            ],
            'x': 10, 'y': 5.5, 'color': '#2ecc71'
        }
    ]
    
    for phase in phases:
        # Vẽ hộp chính
        main_box = FancyBboxPatch((phase['x']-1.4, phase['y']-1.5), 2.8, 3, 
                                 boxstyle="round,pad=0.1", 
                                 facecolor=phase['color'], alpha=0.8)
        ax.add_patch(main_box)
        
        # Tiêu đề giai đoạn
        ax.text(phase['x'], phase['y']+1, phase['title'], 
                ha='center', va='center', fontsize=12, fontweight='bold', color='white')
        ax.text(phase['x'], phase['y']+0.6, phase['subtitle'], 
                ha='center', va='center', fontsize=10, color='white', style='italic')
        
        # Danh sách công việc
        for i, task in enumerate(phase['tasks']):
            ax.text(phase['x'], phase['y']+0.2-i*0.3, task, 
                    ha='center', va='center', fontsize=9, color='white')
    
    # Vẽ mũi tên giữa các giai đoạn
    for i in range(len(phases)-1):
        start_x = phases[i]['x'] + 1.4
        end_x = phases[i+1]['x'] - 1.4
        arrow = ConnectionPatch((start_x, 5.5), (end_x, 5.5), "data", "data",
                              arrowstyle="->", shrinkA=5, shrinkB=5, 
                              mutation_scale=25, fc='black', linewidth=2)
        ax.add_patch(arrow)
    
    # Thêm timeline
    ax.text(6, 2.5, 'TIMELINE TỔNG THỂ: 12 THÁNG', 
            ha='center', va='center', fontsize=14, fontweight='bold')
    
    # Vẽ thanh timeline
    timeline_box = FancyBboxPatch((1, 1.8), 10, 0.4, 
                                 boxstyle="round,pad=0.05", 
                                 facecolor='lightgray', alpha=0.5)
    ax.add_patch(timeline_box)
    
    # Các mốc thời gian
    milestones = [
        ('Tháng 3', 3, '#e74c3c'),
        ('Tháng 6', 6, '#f39c12'),
        ('Tháng 9', 9, '#2ecc71'),
        ('Tháng 12', 11, '#3498db')
    ]
    
    for text, x, color in milestones:
        ax.plot([x, x], [1.8, 2.2], color=color, linewidth=3)
        ax.text(x, 1.5, text, ha='center', va='center', fontsize=10, fontweight='bold')
    
    plt.tight_layout()
    plt.savefig('lo_trinh_trien_khai.png', dpi=300, bbox_inches='tight')
    print("Đã tạo: lo_trinh_trien_khai.png")
    
    return fig

def generate_development_report():
    """Tạo báo cáo phương hướng phát triển"""
    report = []
    
    # Header
    report.append("=" * 80)
    report.append("BÁO CÁO PHƯƠNG HƯỚNG VẬN HÀNH, QUẢN TRỊ VÀ NÂNG CẤP")
    report.append("KHO DỮ LIỆU MỞ THÀNH PHỐ HẢI PHÒNG")
    report.append("=" * 80)
    report.append(f"Ngày lập báo cáo: {datetime.now().strftime('%d/%m/%Y')}")
    report.append("")
    
    # 1. Tình hình hiện tại
    report.append("I. ĐÁNH GIÁ TÌNH HÌNH HIỆN TẠI")
    report.append("-" * 40)
    report.append("1.1. Thành tựu đạt được:")
    report.append("• Vận hành ổn định với 1,224 dataset từ 19 sở ban ngành")
    report.append("• 100% dataset đang hoạt động, thể hiện tính bền vững của hệ thống")
    report.append("• Sự tham gia tích cực của các đơn vị, đặc biệt Sở NN&PTNT (366 dataset)")
    report.append("• Dữ liệu đa dạng với trung bình 6.5 trường/dataset")
    report.append("")
    
    report.append("1.2. Những hạn chế cần khắc phục:")
    report.append("• Tỷ lệ dataset mở công khai thấp (25.9%), chưa đáp ứng mục tiêu chính phủ điện tử")
    report.append("• Thiếu chuẩn hóa: 'diachi' (339 lần) và 'địa chỉ' (62 lần) cùng tồn tại")
    report.append("• Định dạng dữ liệu đơn điệu (99.95% XLSX), khó tích hợp với hệ thống khác")
    report.append("• Một số sở chưa cung cấp tài nguyên dữ liệu (Sở Xây dựng, GTVT: 0%)")
    report.append("• Thiếu kết nối với các hệ thống ngành dọc quan trọng")
    report.append("")
    
    # 2. Phương hướng phát triển
    report.append("II. PHƯƠNG HƯỚNG PHÁT TRIỂN GIAI ĐOẠN 2025-2027")
    report.append("-" * 40)
    report.append("2.1. Mục tiêu tổng thể:")
    report.append("• Xây dựng hệ sinh thái dữ liệu tích hợp, liên thông giữa các ngành")
    report.append("• Nâng tỷ lệ dataset mở lên 60% vào năm 2026")
    report.append("• Tích hợp 5-7 API từ các hệ thống ngành dọc quan trọng")
    report.append("• Chuẩn hóa 100% tên trường và chuyển đổi 70% sang định dạng mở")
    report.append("")
    
    report.append("2.2. Kiến trúc hệ thống mục tiêu:")
    report.append("• API Gateway trung tâm: Chuẩn hóa, xác thực và giám sát tất cả kết nối")
    report.append("• Data Lake: Lưu trữ dữ liệu thô từ nhiều nguồn khác nhau")
    report.append("• Data Warehouse: Dữ liệu đã được xử lý, làm sạch cho phân tích")
    report.append("• Metadata Management: Quản lý thông tin về dữ liệu, lineage, quality")
    report.append("")
    
    # 3. Kế hoạch tích hợp API
    report.append("III. KẾ HOẠCH TÍCH HỢP API CÁC NGÀNH")
    report.append("-" * 40)
    report.append("3.1. API Giáo dục (Ưu tiên cao):")
    report.append("• Kết nối với hệ thống quản lý giáo dục của Bộ GD&ĐT")
    report.append("• Dữ liệu: Học sinh, giáo viên, cơ sở vật chất, kết quả học tập")
    report.append("• Lợi ích: Minh bạch giáo dục, hỗ trợ phụ huynh, quy hoạch giáo dục")
    report.append("")
    
    report.append("3.2. API Quản lý Khu Công nghiệp (Ưu tiên cao):")
    report.append("• Tích hợp với hệ thống quản lý KCN, KKT của thành phố")
    report.append("• Dữ liệu: Doanh nghiệp, đầu tư, việc làm, môi trường")
    report.append("• Lợi ích: Thu hút đầu tư, quản lý môi trường, báo cáo kinh tế")
    report.append("")
    
    report.append("3.3. API Quản lý Cán bộ (Ưu tiên trung bình):")
    report.append("• Kết nối với hệ thống quản lý cán bộ công chức")
    report.append("• Dữ liệu: Biên chế, năng lực, đào tạo, đánh giá hiệu quả")
    report.append("• Lợi ích: Minh bạch nhân sự, tối ưu hóa biên chế")
    report.append("")
    
    report.append("3.4. API Quản lý Công việc - Workflow (Ưu tiên trung bình):")
    report.append("• Tích hợp với hệ thống quản lý công việc nội bộ")
    report.append("• Dữ liệu: Tiến độ dự án, hiệu quả xử lý hồ sơ, KPI")
    report.append("• Lợi ích: Minh bạch hoạt động, cải thiện dịch vụ công")
    report.append("")
    
    # 4. Lộ trình triển khai
    report.append("IV. LỘ TRÌNH TRIỂN KHAI CHI TIẾT")
    report.append("-" * 40)
    report.append("4.1. Giai đoạn 1 - Chuẩn hóa và Tối ưu (Tháng 1-3/2025):")
    report.append("• Tuần 1-4: Phân tích và thiết kế chuẩn dữ liệu thống nhất")
    report.append("• Tuần 5-8: Chuẩn hóa tên trường (diachi → dia_chi, loại bỏ trùng lặp)")
    report.append("• Tuần 9-10: Chuyển đổi 30% dataset sang định dạng CSV/JSON")
    report.append("• Tuần 11-12: Xây dựng API Gateway cơ bản với authentication")
    report.append("• KPI: 40% dataset mở, 30% định dạng chuẩn, 0% trùng lặp tên trường")
    report.append("")
    
    report.append("4.2. Giai đoạn 2 - Tích hợp API Ưu tiên (Tháng 4-9/2025):")
    report.append("• Tháng 4-5: Phát triển connector cho API Giáo dục")
    report.append("• Tháng 6-7: Tích hợp API Quản lý Khu Công nghiệp")
    report.append("• Tháng 8: Xây dựng Data Lake với Apache Spark/Hadoop")
    report.append("• Tháng 9: Phát triển Dashboard BI cho lãnh đạo thành phố")
    report.append("• KPI: 2 API tích hợp, 50% dataset mở, Dashboard hoạt động")
    report.append("")
    
    report.append("4.3. Giai đoạn 3 - Mở rộng và Tối ưu (Tháng 10-12/2025):")
    report.append("• Tháng 10: Tích hợp API Quản lý cán bộ và Workflow")
    report.append("• Tháng 11: Phát triển Mobile App cho người dân")
    report.append("• Tháng 12: Triển khai AI/ML cho dự báo và phân tích xu hướng")
    report.append("• KPI: 4 API tích hợp, 60% dataset mở, Mobile App ra mắt")
    report.append("")
    
    # 5. Nguồn lực và ngân sách
    report.append("V. NGUỒN LỰC VÀ NGÂN SÁCH")
    report.append("-" * 40)
    report.append("5.1. Nhân lực cần thiết:")
    report.append("• 1 Trưởng nhóm dự án (Project Manager)")
    report.append("• 2 Kỹ sư phần mềm Backend (API development)")
    report.append("• 1 Kỹ sư Data Engineer (ETL, Data Pipeline)")
    report.append("• 1 Chuyên gia UI/UX (Dashboard, Mobile App)")
    report.append("• 1 Chuyên viên DevOps (Infrastructure, Security)")
    report.append("")
    
    report.append("5.2. Ước tính ngân sách (12 tháng):")
    report.append("• Nhân lực: 2.4 tỷ VNĐ (200 triệu/tháng)")
    report.append("• Hạ tầng Cloud: 600 triệu VNĐ (50 triệu/tháng)")
    report.append("• Phần mềm License: 300 triệu VNĐ")
    report.append("• Đào tạo và tư vấn: 200 triệu VNĐ")
    report.append("• Tổng cộng: 3.5 tỷ VNĐ")
    report.append("")
    
    # 6. Rủi ro và giải pháp
    report.append("VI. ĐÁNH GIÁ RỦI RO VÀ GIẢI PHÁP")
    report.append("-" * 40)
    report.append("6.1. Rủi ro kỹ thuật:")
    report.append("• Khó khăn tích hợp API do khác biệt chuẩn dữ liệu")
    report.append("  → Giải pháp: Xây dựng Data Adapter layer, chuẩn hóa từng bước")
    report.append("• Hiệu năng hệ thống khi tăng khối lượng dữ liệu")
    report.append("  → Giải pháp: Sử dụng microservices, caching, load balancing")
    report.append("")
    
    report.append("6.2. Rủi ro tổ chức:")
    report.append("• Thiếu sự phối hợp giữa các sở ban ngành")
    report.append("  → Giải pháp: Thành lập Ban chỉ đạo liên ngành, họp định kỳ")
    report.append("• Kháng cự thay đổi từ người dùng")
    report.append("  → Giải pháp: Đào tạo, truyền thông, triển khai từng bước")
    report.append("")
    
    # 7. Kết luận
    report.append("VII. KẾT LUẬN VÀ KHUYẾN NGHỊ")
    report.append("-" * 40)
    report.append("Kho dữ liệu mở Hải Phòng đã có nền tảng vững chắc với 1,224 dataset")
    report.append("từ 19 sở ban ngành. Giai đoạn tiếp theo cần tập trung vào:")
    report.append("")
    report.append("1. Chuẩn hóa dữ liệu và nâng cao chất lượng")
    report.append("2. Tích hợp API từ các hệ thống ngành dọc quan trọng")
    report.append("3. Xây dựng kiến trúc dữ liệu hiện đại với Data Lake/Warehouse")
    report.append("4. Phát triển các ứng dụng phục vụ người dân và doanh nghiệp")
    report.append("")
    report.append("Với lộ trình 12 tháng và ngân sách 3.5 tỷ VNĐ, dự án sẽ nâng")
    report.append("Hải Phòng lên vị trí dẫn đầu về chính phủ số và dữ liệu mở tại Việt Nam.")
    report.append("")
    report.append("=" * 80)
    
    # Lưu báo cáo
    with open('bao_cao_phuong_huong_phat_trien.txt', 'w', encoding='utf-8') as f:
        f.write('\n'.join(report))
    
    print("Đã tạo: bao_cao_phuong_huong_phat_trien.txt")
    return report

def main():
    """Hàm chính"""
    print("TẠO BÁO CÁO PHƯƠNG HƯỚNG PHÁT TRIỂN")
    print("=" * 50)
    
    # Tạo các biểu đồ
    print("1. Tạo biểu đồ luồng dữ liệu tích hợp...")
    create_data_flow_diagram()
    
    print("2. Tạo biểu đồ lộ trình triển khai...")
    create_implementation_roadmap()
    
    print("3. Tạo báo cáo chi tiết...")
    generate_development_report()
    
    print("\n" + "="*50)
    print("HOÀN THÀNH!")
    print("Các file đã tạo:")
    print("- luong_du_lieu_tich_hop.png")
    print("- lo_trinh_trien_khai.png") 
    print("- bao_cao_phuong_huong_phat_trien.txt")
    print("="*50)

if __name__ == "__main__":
    main()
